# Generated by Django 5.1 on 2025-07-01 19:25

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('workflow_config', '0014_alter_formconfig_form_schema'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='aoidailyyield',
            options={'ordering': ['-id']},
        ),
        migrations.AlterModelOptions(
            name='aoirejection',
            options={'ordering': ['-id']},
        ),
        migrations.AlterModelOptions(
            name='area',
            options={'ordering': ['-updated_at']},
        ),
        migrations.AlterModelOptions(
            name='assemblyline',
            options={'ordering': ['-updated_at']},
        ),
        migrations.AlterModelOptions(
            name='factory',
            options={'ordering': ['-updated_at']},
        ),
        migrations.AlterModelOptions(
            name='formconfig',
            options={'ordering': ['-updated_at']},
        ),
        migrations.AlterModelOptions(
            name='masterprogram',
            options={'ordering': ['-updated_at']},
        ),
        migrations.AlterModelOptions(
            name='masterprogramproductparam',
            options={'ordering': ['-updated_at']},
        ),
        migrations.AlterModelOptions(
            name='processblock',
            options={'ordering': ['-updated_at']},
        ),
        migrations.AlterModelOptions(
            name='referencecategory',
            options={'ordering': ['-updated_at'], 'verbose_name_plural': 'Reference Categories'},
        ),
        migrations.AlterModelOptions(
            name='referencevalue',
            options={'ordering': ['-updated_at']},
        ),
        migrations.AlterModelOptions(
            name='routing',
            options={'ordering': ['-updated_at']},
        ),
        migrations.AlterModelOptions(
            name='routingexecution',
            options={'ordering': ['-id']},
        ),
        migrations.AlterModelOptions(
            name='routingproduct',
            options={'ordering': ['-id']},
        ),
        migrations.AlterModelOptions(
            name='sop',
            options={'ordering': ['-updated_at']},
        ),
    ]
