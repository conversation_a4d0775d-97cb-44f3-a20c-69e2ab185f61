from django.db import models
from core.models import BaseModel, BaseEntity
from authentication.models import User


class Dashboard(BaseEntity):
    """
    Dashboard model to store dashboard configurations
    """
    layout = models.JSONField(default=dict, help_text="Dashboard layout configuration")
    is_public = models.BooleanField(default=False, help_text="Whether this dashboard is public or private")
    owner = models.ForeignKey(User, on_delete=models.CASCADE, related_name='dashboards')
    sort_order = models.IntegerField(default=0, help_text="Order in which the dashboard appears (lower numbers appear first)")

    class Meta:
        db_table = 'mes_analytics_dashboards'
        ordering = ['sort_order', 'name']

    def __str__(self):
        return self.name


class ChartGroup(BaseEntity):
    """
    ChartGroup model to organize charts within a dashboard
    """
    dashboard = models.ForeignKey(Dashboard, on_delete=models.CASCADE, related_name='chart_groups')
    description = models.TextField(blank=True, null=True, help_text="Optional description of the chart group")
    sort_order = models.Integer<PERSON>ield(default=0, help_text="Order in which the group appears within a dashboard (lower numbers appear first)")

    class Meta:
        db_table = 'mes_analytics_chart_groups'
        ordering = ['sort_order', 'name']

    def __str__(self):
        return self.name


class Chart(BaseEntity):
    """
    Chart model to store chart configurations
    """
    CHART_TYPES = [
        ('line', 'Line Chart'),
        ('bar', 'Bar Chart'),
        ('pie', 'Pie Chart'),
        ('scatter', 'Scatter Plot'),
        ('table', 'Table'),
        ('gauge', 'Gauge'),
        ('heatmap', 'Heatmap'),
        ('custom', 'Custom'),
    ]

    dashboard = models.ForeignKey(Dashboard, on_delete=models.CASCADE, related_name='charts', null=True, blank=True)
    chart_group = models.ForeignKey(ChartGroup, on_delete=models.SET_NULL, related_name='charts', null=True, blank=True,
                                   help_text="Optional group this chart belongs to")
    chart_type = models.CharField(max_length=20, choices=CHART_TYPES)
    query = models.TextField(null=True, blank=True, help_text="SQL query for the chart data")
    config = models.JSONField(default=dict, help_text="Chart configuration in ECharts format")
    filter_config = models.JSONField(default=dict, help_text="Filter configuration/schema that can be applied to the chart query")
    refresh_interval = models.IntegerField(default=0, help_text="Refresh interval in seconds, 0 means no auto refresh")
    position = models.JSONField(default=dict, help_text="Position in the dashboard grid")
    sort_order = models.IntegerField(default=0, help_text="Order in which the chart appears within a dashboard (lower numbers appear first)")

    class Meta:
        db_table = 'mes_analytics_charts'
        ordering = ['sort_order', 'name']

    def __str__(self):
        return self.name

    def clean(self):
        """
        Validate the chart data before saving
        """
        from django.core.exceptions import ValidationError
        from analytics.validators import SQLQueryValidator

        # Validate SQL query to prevent malicious operations
        if self.query:
            is_valid, error_message = SQLQueryValidator.validate_query(self.query)
            if not is_valid:
                raise ValidationError({'query': f'Invalid SQL query: {error_message}'})

        # Validate filter_config JSON schema
        if self.filter_config:
            self._validate_filter_config()

        super().clean()

    def _validate_filter_config(self):
        """
        Validate the filter_config JSON schema to ensure it follows the required format.

        Supports two formats:
        1. Legacy format: {"column": "type"} where type is "int", "str", "date", "bool"
        2. Enhanced format: {"column": {"type": "int", "clause": "where|having"}}
        """
        from django.core.exceptions import ValidationError

        ALLOWED_TYPES = ["int", "str", "date", "bool"]
        ALLOWED_CLAUSES = ["where", "having"]

        if not isinstance(self.filter_config, dict):
            raise ValidationError({'filter_config': 'Filter configuration must be a JSON object'})

        for key, value in self.filter_config.items():
            # Check for legacy format (string value)
            if isinstance(value, str):
                if value not in ALLOWED_TYPES:
                    raise ValidationError({
                        'filter_config': f'Value for key "{key}" must be one of: {", ".join(ALLOWED_TYPES)}'
                    })

            # Check for enhanced format (dict value)
            elif isinstance(value, dict):
                # Validate required 'type' field
                if 'type' not in value:
                    raise ValidationError({
                        'filter_config': f'Enhanced format for key "{key}" must include "type" field'
                    })

                if not isinstance(value['type'], str):
                    raise ValidationError({
                        'filter_config': f'Type for key "{key}" must be a string'
                    })

                if value['type'] not in ALLOWED_TYPES:
                    raise ValidationError({
                        'filter_config': f'Type for key "{key}" must be one of: {", ".join(ALLOWED_TYPES)}'
                    })

                # Validate optional 'clause' field
                if 'clause' in value:
                    if not isinstance(value['clause'], str):
                        raise ValidationError({
                            'filter_config': f'Clause for key "{key}" must be a string'
                        })

                    if value['clause'] not in ALLOWED_CLAUSES:
                        raise ValidationError({
                            'filter_config': f'Clause for key "{key}" must be one of: {", ".join(ALLOWED_CLAUSES)}'
                        })

                # Validate optional 'label' field
                if 'label' in value:
                    if not isinstance(value['label'], str):
                        raise ValidationError({
                            'filter_config': f'Label for key "{key}" must be a string'
                        })
                
                # Check for unexpected fields
                allowed_fields = {'type', 'clause', 'label'}
                unexpected_fields = set(value.keys()) - allowed_fields
                if unexpected_fields:
                    raise ValidationError({
                        'filter_config': f'Unexpected fields for key "{key}": {", ".join(unexpected_fields)}'
                    })

            else:
                raise ValidationError({
                    'filter_config': f'Value for key "{key}" must be either a string (legacy format) or a dict (enhanced format)'
                })

    def get_normalized_filter_config(self):
        """
        Get the filter configuration in normalized enhanced format.

        Converts legacy format to enhanced format for consistent processing.

        Returns:
            Dict with normalized filter configuration where each value is a dict with 'type' and 'clause' keys
        """
        normalized_config = {}

        for key, value in self.filter_config.items():
            if isinstance(value, str):
                # Legacy format - convert to enhanced format with default 'where' clause
                normalized_config[key] = {
                    'type': value,
                    'clause': 'where',
                    'label': ''
                }
            elif isinstance(value, dict):
                # Enhanced format - ensure 'clause' field exists with default 'where'
                normalized_config[key] = {
                    'type': value['type'],
                    'clause': value.get('clause', 'where')
                }
                
                # Add label if it exists
                if 'label' in value:
                    normalized_config[key]['label'] = value['label']

        return normalized_config

    def save(self, *args, **kwargs):
        """
        Override save method to ensure validation is performed
        """
        self.clean()
        super().save(*args, **kwargs)