import json
import requests
import pandas as pd
from django.conf import settings
from django.db import connections
from typing import Dict, List, Any, Optional


class ClickHouseService:
    """
    Service for interacting with ClickHouse database using Django's database connections
    """
    def __init__(self, using='clickhouse'):
        # Use Django's database connection pool
        self.using = using

    def execute_query(self, query: str) -> pd.DataFrame:
        """
        Execute a SQL query and return the results as a pandas DataFrame

        The query is first validated to ensure it doesn't contain dangerous operations
        """
        from analytics.validators import SQLQueryValidator

        # Validate the query before execution
        is_valid, error_message = SQLQueryValidator.validate_query(query)
        if not is_valid:
            print(f"Query validation failed: {error_message}")
            return pd.DataFrame({
                'error': [f"Query validation failed: {error_message}"],
                'query': [query]
            })

        try:
            # Use Django's database connection
            with connections[self.using].cursor() as cursor:
                cursor.execute(query)
                # Get column names from cursor description
                columns = [col[0] for col in cursor.description] if cursor.description else []
                # Fetch all rows
                rows = cursor.fetchall()
                # Create DataFrame
                df = pd.DataFrame(rows, columns=columns)
                return df
        except Exception as e:
            print(f"Error executing query: {str(e)}")
            return pd.DataFrame()


class ChartTransformerBase:
    """
    Base class for chart transformers using Strategy Pattern
    """
    @staticmethod
    def handle_error_or_empty(df: pd.DataFrame) -> Optional[Dict]:
        """
        Handle error or empty dataframes

        Returns None if no error, otherwise returns error config
        """
        if 'error' in df.columns:
            return {
                "title": {"text": "Query Error"},
                "tooltip": {"formatter": df['error'].iloc[0] if not df.empty else "Unknown error"},
                "series": []
            }

        if df.empty:
            return {
                "title": {"text": "No Data Available"},
                "series": []
            }

        return None

    @staticmethod
    def ensure_config_is_dict(config) -> Dict:
        """
        Ensure config is a dictionary, return empty dict if not
        """
        if isinstance(config, dict):
            return config.copy()
        else:
            # If config is not a dict (e.g., it's a list or None), return empty dict
            return {}

    @staticmethod
    def create_dataset(df: pd.DataFrame) -> Dict:
        """
        Create a dataset configuration from a DataFrame

        Returns a dataset configuration that can be used in ECharts
        """
        if df.empty:
            return {}

        # Convert DataFrame to dataset source format
        # First row is column names, subsequent rows are data
        source = [df.columns.tolist()]  # First row is column names
        for _, row in df.iterrows():
            source.append(row.tolist())  # Add data rows

        return {
            "dimensions": df.columns.tolist(),
            "source": source
        }

    @staticmethod
    def transform(df: pd.DataFrame, config: Dict) -> Dict:
        """
        Transform dataframe to chart format
        """
        raise NotImplementedError("Subclasses must implement transform method")


class LineBarTransformer(ChartTransformerBase):
    """
    Transformer for line and bar charts using dataset approach
    """
    @staticmethod
    def transform(df: pd.DataFrame, config: Dict, chart_type: str) -> Dict:
        result = ChartTransformerBase.ensure_config_is_dict(config)

        # Create dataset from DataFrame if not empty
        if not df.empty:
            # Create dataset configuration
            dataset_config = ChartTransformerBase.create_dataset(df)

            # Add or update dataset in result
            if "dataset" not in result:
                result["dataset"] = dataset_config
            else:
                # If dataset already exists in config, update it only if it's a dict
                if isinstance(result["dataset"], dict):
                    result["dataset"].update(dataset_config)
                else:
                    # If existing dataset is not a dict (e.g., it's a list), replace it
                    result["dataset"] = dataset_config

        # Update or create xAxis configuration
        if "xAxis" not in result:
            result["xAxis"] = {"type": "category"}
        elif not isinstance(result["xAxis"], dict):
            # If xAxis is not a dict (e.g., it's a list), replace it
            result["xAxis"] = {"type": "category"}
        elif "type" not in result["xAxis"]:
            result["xAxis"]["type"] = "category"

        # Remove data from xAxis if it exists (now using dataset)
        if "data" in result.get("xAxis", {}):
            del result["xAxis"]["data"]

        # Update or create yAxis configuration
        if "yAxis" not in result:
            result["yAxis"] = {"type": "value"}
        elif not isinstance(result["yAxis"], dict):
            # If yAxis is not a dict (e.g., it's a list), replace it
            result["yAxis"] = {"type": "value"}

        # Create or update series without data (data comes from dataset)
        if "series" not in result or not isinstance(result["series"], list) or not result["series"]:
            # No existing series, create new ones based on columns (excluding first column which is for x-axis)
            result["series"] = []
            if not df.empty:
                for col in df.columns[1:]:
                    result["series"].append({
                        "name": col,
                        "type": chart_type,
                        # No data here, it comes from dataset
                        "encode": {
                            "x": df.columns[0],  # First column is x-axis
                            "y": col             # Current column is y-axis
                        }
                    })
        else:
            # Update existing series to use dataset
            existing_series_count = len(result["series"])

            # First update existing series
            for i, series_item in enumerate(result["series"]):
                # Remove data from series if it exists
                if "data" in series_item:
                    del series_item["data"]

                # Ensure type is set correctly
                series_item["type"] = chart_type

                # Add encode if not empty dataframe and we have enough columns
                if not df.empty and i < len(df.columns) - 1:
                    series_item["encode"] = {
                        "x": df.columns[0],      # First column is x-axis
                        "y": df.columns[i + 1]   # Column for this series
                    }

            # Add new series for any additional columns in the dataframe
            if not df.empty and len(df.columns) > existing_series_count + 1:
                for i in range(existing_series_count, len(df.columns) - 1):
                    col = df.columns[i + 1]
                    result["series"].append({
                        "name": col,
                        "type": chart_type,
                        "encode": {
                            "x": df.columns[0],  # First column is x-axis
                            "y": col             # Current column is y-axis
                        }
                    })

        return result


class PieTransformer(ChartTransformerBase):
    """
    Transformer for pie charts using dataset approach
    """
    @staticmethod
    def transform(df: pd.DataFrame, config: Dict) -> Dict:
        result = ChartTransformerBase.ensure_config_is_dict(config)

        # Create dataset from DataFrame if not empty
        if not df.empty:
            # Create dataset configuration
            dataset_config = ChartTransformerBase.create_dataset(df)

            # Add or update dataset in result
            if "dataset" not in result:
                result["dataset"] = dataset_config
            else:
                # If dataset already exists in config, update it only if it's a dict
                if isinstance(result["dataset"], dict):
                    result["dataset"].update(dataset_config)
                else:
                    # If existing dataset is not a dict (e.g., it's a list), replace it
                    result["dataset"] = dataset_config

        # Create or update series without data (data comes from dataset)
        if "series" not in result or not isinstance(result["series"], list) or not result["series"]:
            # No existing series, create new one
            result["series"] = [{
                "type": "pie",
                # No data here, it comes from dataset
                "encode": {
                    "itemName": df.columns[0] if not df.empty else "name",  # First column for item names
                    "value": df.columns[1] if not df.empty and len(df.columns) > 1 else "value"  # Second column for values
                }
            }]
        else:
            # Update existing series to use dataset
            pie_series_found = False
            for series_item in result["series"]:
                if series_item.get("type") == "pie":
                    # Remove data from series if it exists
                    if "data" in series_item:
                        del series_item["data"]

                    # Add encode if not already present
                    if "encode" not in series_item and not df.empty:
                        series_item["encode"] = {
                            "itemName": df.columns[0],  # First column for item names
                            "value": df.columns[1] if len(df.columns) > 1 else df.columns[0]  # Second column for values
                        }

                    pie_series_found = True
                    break

            # If no pie series found, add a new one
            if not pie_series_found:
                result["series"].append({
                    "type": "pie",
                    "encode": {
                        "itemName": df.columns[0] if not df.empty else "name",
                        "value": df.columns[1] if not df.empty and len(df.columns) > 1 else "value"
                    }
                })

        return result


class TableTransformer(ChartTransformerBase):
    """
    Transformer for table format
    """
    @staticmethod
    def transform(df: pd.DataFrame, config: Dict) -> Dict:
        result = ChartTransformerBase.ensure_config_is_dict(config)

        # For tables, we still need to return in a specific format
        # but we'll preserve any existing config and add dataset if available
        if not df.empty:
            # Add dataset for reference
            dataset_config = ChartTransformerBase.create_dataset(df)
            if "dataset" not in result:
                result["dataset"] = dataset_config
            else:
                # If dataset already exists in config, update it only if it's a dict
                if isinstance(result["dataset"], dict):
                    result["dataset"].update(dataset_config)
                else:
                    # If existing dataset is not a dict (e.g., it's a list), replace it
                    result["dataset"] = dataset_config

        # Always include columns and data for tables
        result["columns"] = df.columns.tolist() if not df.empty else result.get("columns", [])
        result["data"] = df.values.tolist() if not df.empty else result.get("data", [])

        return result


class FilterService:
    """
    Service for handling SQL query filters with clause-aware filtering
    """
    @staticmethod
    def build_filter_conditions(filter_config: Dict, query_params: Dict) -> Dict[str, List[str]]:
        """
        Build filter conditions based on filter configuration and query parameters.

        Now returns conditions grouped by SQL clause (WHERE/HAVING).

        Args:
            filter_config: Dictionary mapping filter keys to their types (legacy) or config objects (enhanced)
            query_params: Request query parameters

        Returns:
            Dictionary with 'where' and 'having' keys containing lists of SQL filter conditions
        """
        filter_conditions = {
            'where': [],
            'having': []
        }
        print("__filter_config__", filter_config)
        print("__query_params__", query_params)

        if not filter_config or not query_params:
            return filter_conditions

        # Normalize filter config to handle both legacy and enhanced formats
        normalized_config = FilterService._normalize_filter_config(filter_config)

        for filter_key, config in normalized_config.items():
            if filter_key in query_params:
                filter_value = query_params.get(filter_key)
                filter_type = config['type']
                clause = config['clause']

                print("__filter_value__", filter_key, filter_value, f"type={filter_type}", f"clause={clause}")

                # Build the condition based on filter type
                condition = FilterService._build_single_condition(filter_key, filter_type, filter_value, query_params)

                if condition:
                    filter_conditions[clause].append(condition)

        print("__filter_conditions__", filter_conditions)
        return filter_conditions

    @staticmethod
    def _normalize_filter_config(filter_config: Dict) -> Dict:
        """
        Normalize filter configuration to enhanced format for consistent processing.

        Args:
            filter_config: Raw filter configuration (legacy or enhanced format)

        Returns:
            Normalized configuration where each value is a dict with 'type' and 'clause' keys
        """
        normalized_config = {}

        for key, value in filter_config.items():
            if isinstance(value, str):
                # Legacy format - convert to enhanced format with default 'where' clause
                normalized_config[key] = {
                    'type': value,
                    'clause': 'where'
                }
            elif isinstance(value, dict):
                # Enhanced format - ensure 'clause' field exists with default 'where'
                normalized_config[key] = {
                    'type': value['type'],
                    'clause': value.get('clause', 'where')
                }

        return normalized_config

    @staticmethod
    def _build_single_condition(filter_key: str, filter_type: str, filter_value: str, query_params: Dict) -> str:
        """
        Build a single filter condition based on the filter type and value.

        Args:
            filter_key: The filter key/column name
            filter_type: The data type of the filter ('int', 'str', 'date', 'bool')
            filter_value: The filter value from query parameters
            query_params: Full query parameters for accessing list values

        Returns:
            SQL condition string or None if invalid
        """
        try:
            # Process filter based on its type
            if filter_type in ['date', 'int']:
                values = query_params.getlist(filter_key)  # Get in list format, either single value or range
                if not isinstance(values, list):
                    return None

                if len(values) == 1:
                    # Exact match
                    if filter_type == 'int':
                        # Convert to integer if it's a string
                        if isinstance(values[0], str):
                            values[0] = int(values[0])
                        return f"{filter_key} = {values[0]}"
                    else:  # date type
                        return f"{filter_key} = '{values[0]}'"

                elif len(values) == 2:
                    # Range match
                    if filter_type == 'int':
                        # Convert to integers if they're strings
                        if isinstance(values[0], str):
                            values[0] = int(values[0])
                        if isinstance(values[1], str):
                            values[1] = int(values[1])
                        # Sort values to ensure smaller value comes first
                        min_val, max_val = sorted([values[0], values[1]])
                        return f"{filter_key} BETWEEN {min_val} AND {max_val}"
                    else:  # date type
                        # Sort date values to ensure earlier date comes first
                        min_date, max_date = sorted([values[0], values[1]])
                        return f"{filter_key} BETWEEN '{min_date}' AND '{max_date}'"

            elif filter_type == 'bool':
                # Boolean filter (0 or 1)
                if filter_value in ['0', '1']:
                    return f"{filter_key} = {filter_value}"

            elif filter_type == 'str':
                # String filter
                if '%' in filter_value:
                    # LIKE operator for partial matches
                    return f"{filter_key} LIKE '{filter_value}'"
                else:
                    # Exact match
                    return f"{filter_key} = '{filter_value}'"

        except (json.JSONDecodeError, ValueError, TypeError) as e:
            print(f"Error processing filter {filter_key}: {str(e)}")
            return None

        return None

    @staticmethod
    def apply_filters_to_query(query: str, filter_conditions: Dict[str, List[str]]) -> str:
        """
        Apply filter conditions to a SQL query with clause-aware filtering.

        Args:
            query: Original SQL query
            filter_conditions: Dictionary with 'where' and 'having' keys containing lists of SQL filter conditions

        Returns:
            Modified SQL query with filters applied to appropriate clauses
        """
        # Handle backward compatibility - if filter_conditions is a list, treat as WHERE conditions
        if isinstance(filter_conditions, list):
            filter_conditions = {'where': filter_conditions, 'having': []}

        where_conditions = filter_conditions.get('where', [])
        having_conditions = filter_conditions.get('having', [])

        # Apply WHERE conditions
        if where_conditions:
            query = FilterService._apply_where_conditions(query, where_conditions)

        # Apply HAVING conditions
        if having_conditions:
            query = FilterService._apply_having_conditions(query, having_conditions)

        return query

    @staticmethod
    def _apply_where_conditions(query: str, where_conditions: List[str]) -> str:
        """
        Apply WHERE conditions to a SQL query.

        Args:
            query: Original SQL query
            where_conditions: List of WHERE filter conditions

        Returns:
            Modified SQL query with WHERE conditions applied
        """
        if not where_conditions:
            return query

        # Check if the query already has a WHERE clause
        if 'WHERE' in query.upper():
            # Find the WHERE clause and wrap existing condition
            where_pos = query.upper().find('WHERE')
            before_where = query[:where_pos]
            after_where = query[where_pos + 5:].strip()  # Remove 'WHERE' and strip spaces

            # Find the end of the WHERE clause (before GROUP BY, ORDER BY, etc.)
            clauses = ['GROUP BY', 'ORDER BY', 'LIMIT', 'HAVING']
            where_end = len(after_where)

            for clause in clauses:
                clause_pos = after_where.upper().find(clause)
                if clause_pos != -1 and clause_pos < where_end:
                    where_end = clause_pos

            existing_where = after_where[:where_end].strip()
            remaining_query = after_where[where_end:].strip()

            # Reconstruct query with wrapped WHERE condition
            query = before_where + 'WHERE (' + existing_where + ') AND ' + ' AND '.join(where_conditions)
            if remaining_query:
                query += ' ' + remaining_query
        else:
            # Add WHERE clause with filters
            # Find appropriate position to add WHERE clause (after FROM clause and before GROUP BY, ORDER BY, etc.)
            clauses = ['GROUP BY', 'ORDER BY', 'LIMIT', 'HAVING']
            position = len(query.strip())

            for clause in clauses:
                clause_pos = query.upper().find(clause)
                if clause_pos != -1 and clause_pos < position:
                    position = clause_pos

            # Insert WHERE clause at the determined position
            before_clause = query[:position].strip()
            after_clause = query[position:].strip()

            query = before_clause + ' WHERE ' + ' AND '.join(where_conditions)
            if after_clause:
                query += ' ' + after_clause

        return query

    @staticmethod
    def _apply_having_conditions(query: str, having_conditions: List[str]) -> str:
        """
        Apply HAVING conditions to a SQL query.

        Args:
            query: Original SQL query
            having_conditions: List of HAVING filter conditions

        Returns:
            Modified SQL query with HAVING conditions applied
        """
        if not having_conditions:
            return query

        # Check if the query already has a HAVING clause
        if 'HAVING' in query.upper():
            # Find the HAVING clause and wrap existing condition
            having_pos = query.upper().find('HAVING')
            before_having = query[:having_pos]
            after_having = query[having_pos + 6:].strip()  # Remove 'HAVING' and strip spaces

            # Find the end of the HAVING clause (before ORDER BY, LIMIT)
            clauses = ['ORDER BY', 'LIMIT']
            having_end = len(after_having)

            for clause in clauses:
                clause_pos = after_having.upper().find(clause)
                if clause_pos != -1 and clause_pos < having_end:
                    having_end = clause_pos

            existing_having = after_having[:having_end].strip()
            remaining_query = after_having[having_end:].strip()

            # Reconstruct query with wrapped HAVING condition
            query = before_having + 'HAVING (' + existing_having + ') AND ' + ' AND '.join(having_conditions)
            if remaining_query:
                query += ' ' + remaining_query
        else:
            # Add HAVING clause with filters
            # Find appropriate position to add HAVING clause (after GROUP BY and before ORDER BY, LIMIT)
            clauses = ['ORDER BY', 'LIMIT']
            position = len(query.strip())

            for clause in clauses:
                clause_pos = query.upper().find(clause)
                if clause_pos != -1 and clause_pos < position:
                    position = clause_pos

            # Insert HAVING clause at the determined position
            before_clause = query[:position].strip()
            after_clause = query[position:].strip()

            query = before_clause + ' HAVING ' + ' AND '.join(having_conditions)
            if after_clause:
                query += ' ' + after_clause

        return query


class ChartService:
    """
    Service for transforming data into chart format
    """
    @staticmethod
    def get_chart_data(chart) -> pd.DataFrame:
        """
        Get data for a chart by executing its query

        Args:
            chart: Chart model instance

        Returns:
            DataFrame with chart data or error information
        """
        clickhouse_service = ClickHouseService()
        return clickhouse_service.execute_query(chart.query)

    @staticmethod
    def get_charts_by_dashboard(dashboard_id):
        """
        Get all charts for a dashboard, including those in chart groups

        Args:
            dashboard_id: ID of the dashboard

        Returns:
            List of chart objects ordered by sort_order
        """
        from analytics.models import Chart, ChartGroup

        # Get charts directly attached to dashboard
        direct_charts = Chart.objects.filter(dashboard_id=dashboard_id).order_by('sort_order')

        # Get charts from chart groups in this dashboard
        chart_groups = ChartGroup.objects.filter(dashboard_id=dashboard_id).order_by('sort_order')
        group_charts = Chart.objects.filter(chart_group__in=chart_groups).order_by('chart_group__sort_order', 'sort_order')

        # Combine both querysets
        return list(direct_charts) + list(group_charts)

    @staticmethod
    def get_charts_by_chart_group(chart_group_id):
        """
        Get all charts for a specific chart group

        Args:
            chart_group_id: ID of the chart group

        Returns:
            List of chart objects ordered by sort_order
        """
        from analytics.models import Chart

        return Chart.objects.filter(chart_group_id=chart_group_id).order_by('sort_order')

    @staticmethod
    def transform_to_echarts(df: pd.DataFrame, chart_type: str, config: Dict) -> Dict:
        """
        Transform pandas DataFrame to ECharts format based on chart type

        If data is not available from the datasource but sample data exists in config,
        the config with sample data will be returned without modification.
        """
        # Ensure config is a valid dictionary
        safe_config = ChartTransformerBase.ensure_config_is_dict(config)

        # Check if we have a dataset in the config already (sample data)
        has_sample_data = False
        if "dataset" in safe_config and safe_config["dataset"]:
        # and (
        #     ("source" in safe_config["dataset"] and safe_config["dataset"]["source"]) or
        #     ("dimensions" in safe_config["dataset"] and safe_config["dataset"]["dimensions"])
        # ):
            has_sample_data = True

        # Check for errors in the dataframe
        if 'error' in df.columns:
            error_result = ChartTransformerBase.handle_error_or_empty(df)
            if error_result:
                return error_result

        # If dataframe is empty but we have sample data in config, return config as is
        if df.empty and has_sample_data:
            return safe_config

        # If dataframe is empty and no sample data, show empty message
        if df.empty and not has_sample_data:
            return {
                "title": {"text": "No Data Available"},
                "series": []
            }

        # Use the appropriate transformer based on chart type
        if chart_type == 'line' or chart_type == 'bar':
            return LineBarTransformer.transform(df, safe_config, chart_type)
        elif chart_type == 'pie':
            return PieTransformer.transform(df, safe_config)
        elif chart_type == 'table':
            return TableTransformer.transform(df, safe_config)
        else:
            # Default case for unsupported chart types
            # Add dataset to config if data is available
            result = safe_config.copy()
            if not df.empty:
                dataset_config = ChartTransformerBase.create_dataset(df)
                if "dataset" not in result:
                    result["dataset"] = dataset_config
                else:
                    # If dataset already exists in config, update it only if it's a dict
                    if isinstance(result["dataset"], dict):
                        result["dataset"].update(dataset_config)
                    else:
                        # If existing dataset is not a dict (e.g., it's a list), replace it
                        result["dataset"] = dataset_config
            return result
