# Generated by Django 5.1 on 2025-07-01 19:25

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('authentication', '0003_groupmodulepermission_allow_all_objects_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='accessscope',
            options={'ordering': ['-id']},
        ),
        migrations.AlterModelOptions(
            name='group',
            options={'ordering': ['-id']},
        ),
        migrations.AlterModelOptions(
            name='groupmodulepermission',
            options={'ordering': ['-id']},
        ),
        migrations.AlterModelOptions(
            name='groupobjectpermission',
            options={'ordering': ['-id']},
        ),
        migrations.AlterModelOptions(
            name='module',
            options={'ordering': ['-id']},
        ),
        migrations.AlterModelOptions(
            name='user',
            options={'ordering': ['-id']},
        ),
        migrations.AlterModelOptions(
            name='usergroup',
            options={'ordering': ['-id']},
        ),
        migrations.AlterModelOptions(
            name='usermodulepermission',
            options={'ordering': ['-id']},
        ),
        migrations.AlterModelOptions(
            name='userobjectpermission',
            options={'ordering': ['-id']},
        ),
    ]
